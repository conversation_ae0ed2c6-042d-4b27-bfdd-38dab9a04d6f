{"format": 1, "restore": {"D:\\APP\\app-prog\\NetworkManagement.csproj": {}}, "projects": {"D:\\APP\\app-prog\\NetworkManagement.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\APP\\app-prog\\NetworkManagement.csproj", "projectName": "NetworkManagement", "projectPath": "D:\\APP\\app-prog\\NetworkManagement.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\APP\\app-prog\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "EPPlus": {"target": "Package", "version": "[7.0.0, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.7, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[6.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.7, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.39, )"}, "MySqlConnector": {"target": "Package", "version": "[2.1.13, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[6.0.2, )"}, "QuestPDF": {"target": "Package", "version": "[2023.12.6, )"}, "System.Drawing.Common": {"target": "Package", "version": "[7.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.3.25201.16\\RuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(, 4.7.0]", "Microsoft.NETCore.App": "(, 2.1.0]", "Microsoft.VisualBasic": "(, 10.3.0]", "Microsoft.Win32.Primitives": "(, 4.3.0]", "Microsoft.Win32.Registry": "(, 5.0.0]", "runtime.any.System.Collections": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.any.System.Globalization": "(, 4.3.0]", "runtime.any.System.Globalization.Calendars": "(, 4.3.0]", "runtime.any.System.IO": "(, 4.3.0]", "runtime.any.System.Reflection": "(, 4.3.0]", "runtime.any.System.Reflection.Extensions": "(, 4.3.0]", "runtime.any.System.Reflection.Primitives": "(, 4.3.0]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.any.System.Runtime": "(, 4.3.1]", "runtime.any.System.Runtime.Handles": "(, 4.3.0]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.any.System.Text.Encoding": "(, 4.3.0]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.any.System.Threading.Tasks": "(, 4.3.0]", "runtime.any.System.Threading.Timer": "(, 4.3.0]", "runtime.aot.System.Collections": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.aot.System.Globalization": "(, 4.3.0]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.0]", "runtime.aot.System.IO": "(, 4.3.0]", "runtime.aot.System.Reflection": "(, 4.3.0]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.0]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.0]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.aot.System.Runtime": "(, 4.3.1]", "runtime.aot.System.Runtime.Handles": "(, 4.3.0]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.aot.System.Text.Encoding": "(, 4.3.0]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.aot.System.Threading.Tasks": "(, 4.3.0]", "runtime.aot.System.Threading.Timer": "(, 4.3.0]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.unix.System.Console": "(, 4.3.1]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.unix.System.IO.FileSystem": "(, 4.3.0]", "runtime.unix.System.Net.Primitives": "(, 4.3.0]", "runtime.unix.System.Net.Sockets": "(, 4.3.0]", "runtime.unix.System.Private.Uri": "(, 4.3.1]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.win.System.Console": "(, 4.3.1]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.win.System.IO.FileSystem": "(, 4.3.0]", "runtime.win.System.Net.Primitives": "(, 4.3.0]", "runtime.win.System.Net.Sockets": "(, 4.3.0]", "runtime.win.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7.System.Private.Uri": "(, 4.3.1]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.2]", "System.AppContext": "(, 4.3.0]", "System.Buffers": "(, 4.5.1]", "System.Collections": "(, 4.3.0]", "System.Collections.Concurrent": "(, 4.3.0]", "System.Collections.Immutable": "(, 6.0.0]", "System.Collections.NonGeneric": "(, 4.3.0]", "System.Collections.Specialized": "(, 4.3.0]", "System.ComponentModel": "(, 4.3.0]", "System.ComponentModel.Annotations": "(, 5.0.0]", "System.ComponentModel.EventBasedAsync": "(, 4.3.0]", "System.ComponentModel.Primitives": "(, 4.3.0]", "System.ComponentModel.TypeConverter": "(, 4.3.0]", "System.Console": "(, 4.3.1]", "System.Data.Common": "(, 4.3.0]", "System.Data.DataSetExtensions": "(, 4.5.0]", "System.Diagnostics.Contracts": "(, 4.3.0]", "System.Diagnostics.Debug": "(, 4.3.0]", "System.Diagnostics.DiagnosticSource": "(, 6.0.0]", "System.Diagnostics.FileVersionInfo": "(, 4.3.0]", "System.Diagnostics.Process": "(, 4.3.0]", "System.Diagnostics.StackTrace": "(, 4.3.0]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.0]", "System.Diagnostics.Tools": "(, 4.3.0]", "System.Diagnostics.TraceSource": "(, 4.3.0]", "System.Diagnostics.Tracing": "(, 4.3.0]", "System.Drawing.Primitives": "(, 4.3.0]", "System.Dynamic.Runtime": "(, 4.3.0]", "System.Formats.Asn1": "(, 6.0.0]", "System.Globalization": "(, 4.3.0]", "System.Globalization.Calendars": "(, 4.3.0]", "System.Globalization.Extensions": "(, 4.3.0]", "System.IO": "(, 4.3.0]", "System.IO.Compression": "(, 4.3.0]", "System.IO.Compression.ZipFile": "(, 4.3.0]", "System.IO.FileSystem": "(, 4.3.0]", "System.IO.FileSystem.AccessControl": "(, 5.0.0]", "System.IO.FileSystem.DriveInfo": "(, 4.3.1]", "System.IO.FileSystem.Primitives": "(, 4.3.0]", "System.IO.FileSystem.Watcher": "(, 4.3.0]", "System.IO.IsolatedStorage": "(, 4.3.0]", "System.IO.MemoryMappedFiles": "(, 4.3.0]", "System.IO.Pipes": "(, 4.3.0]", "System.IO.Pipes.AccessControl": "(, 4.6.0]", "System.IO.UnmanagedMemoryStream": "(, 4.3.0]", "System.Linq": "(, 4.3.0]", "System.Linq.Expressions": "(, 4.3.0]", "System.Linq.Parallel": "(, 4.3.0]", "System.Linq.Queryable": "(, 4.3.0]", "System.Memory": "(, 4.5.5]", "System.Net.Http": "(, 4.3.4]", "System.Net.Http.Json": "(, 6.0.0]", "System.Net.NameResolution": "(, 4.3.0]", "System.Net.NetworkInformation": "(, 4.3.0]", "System.Net.Ping": "(, 4.3.0]", "System.Net.Primitives": "(, 4.3.1]", "System.Net.Requests": "(, 4.3.0]", "System.Net.Security": "(, 4.3.2]", "System.Net.Sockets": "(, 4.3.0]", "System.Net.WebHeaderCollection": "(, 4.3.0]", "System.Net.WebSockets": "(, 4.3.0]", "System.Net.WebSockets.Client": "(, 4.3.2]", "System.Numerics.Vectors": "(, 4.5.0]", "System.ObjectModel": "(, 4.3.0]", "System.Private.DataContractSerialization": "(, 4.3.0]", "System.Private.Uri": "(, 4.3.0]", "System.Reflection": "(, 4.3.0]", "System.Reflection.DispatchProxy": "(, 4.7.1]", "System.Reflection.Emit": "(, 4.7.0]", "System.Reflection.Emit.ILGeneration": "(, 4.7.0]", "System.Reflection.Emit.Lightweight": "(, 4.7.0]", "System.Reflection.Extensions": "(, 4.3.0]", "System.Reflection.Metadata": "(, 6.0.0]", "System.Reflection.Primitives": "(, 4.3.0]", "System.Reflection.TypeExtensions": "(, 4.7.0]", "System.Resources.Reader": "(, 4.3.0]", "System.Resources.ResourceManager": "(, 4.3.0]", "System.Resources.Writer": "(, 4.3.0]", "System.Runtime": "(, 4.3.1]", "System.Runtime.CompilerServices.Unsafe": "(, 6.0.0]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.0]", "System.Runtime.Extensions": "(, 4.3.1]", "System.Runtime.Handles": "(, 4.3.0]", "System.Runtime.InteropServices": "(, 4.3.0]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.0]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.0]", "System.Runtime.Loader": "(, 4.3.0]", "System.Runtime.Numerics": "(, 4.3.0]", "System.Runtime.Serialization.Formatters": "(, 4.3.0]", "System.Runtime.Serialization.Json": "(, 4.3.0]", "System.Runtime.Serialization.Primitives": "(, 4.3.0]", "System.Runtime.Serialization.Xml": "(, 4.3.0]", "System.Runtime.WindowsRuntime": "(, 4.7.0]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.0]", "System.Security.AccessControl": "(, 6.0.0]", "System.Security.Claims": "(, 4.3.0]", "System.Security.Cryptography.Algorithms": "(, 4.3.1]", "System.Security.Cryptography.Cng": "(, 4.6.0]", "System.Security.Cryptography.Csp": "(, 4.3.0]", "System.Security.Cryptography.Encoding": "(, 4.3.0]", "System.Security.Cryptography.OpenSsl": "(, 5.0.0]", "System.Security.Cryptography.Primitives": "(, 4.3.0]", "System.Security.Cryptography.X509Certificates": "(, 4.3.2]", "System.Security.Cryptography.Xml": "(, 4.4.0]", "System.Security.Principal": "(, 4.3.0]", "System.Security.Principal.Windows": "(, 5.0.0]", "System.Security.SecureString": "(, 4.3.0]", "System.Text.Encoding": "(, 4.3.0]", "System.Text.Encoding.CodePages": "(, 6.0.0]", "System.Text.Encoding.Extensions": "(, 4.3.0]", "System.Text.Encodings.Web": "(, 6.0.0]", "System.Text.Json": "(, 6.0.0]", "System.Text.RegularExpressions": "(, 4.3.1]", "System.Threading": "(, 4.3.0]", "System.Threading.Channels": "(, 6.0.0]", "System.Threading.Overlapped": "(, 4.3.0]", "System.Threading.Tasks": "(, 4.3.0]", "System.Threading.Tasks.Dataflow": "(, 6.0.0]", "System.Threading.Tasks.Extensions": "(, 4.5.4]", "System.Threading.Tasks.Parallel": "(, 4.3.0]", "System.Threading.Thread": "(, 4.3.0]", "System.Threading.ThreadPool": "(, 4.3.0]", "System.Threading.Timer": "(, 4.3.0]", "System.ValueTuple": "(, 4.5.0]", "System.Xml.ReaderWriter": "(, 4.3.1]", "System.Xml.XDocument": "(, 4.3.0]", "System.Xml.XmlDocument": "(, 4.3.0]", "System.Xml.XmlSerializer": "(, 4.3.0]", "System.Xml.XPath": "(, 4.3.0]", "System.Xml.XPath.XDocument": "(, 4.3.0]"}}}}}}