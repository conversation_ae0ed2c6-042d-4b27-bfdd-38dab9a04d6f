using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class DeviceDialog : Window
    {
        public DeviceDialog()
        {
            InitializeComponent();
        }

        public DeviceDialog(DeviceDialogViewModel viewModel) : this()
        {
            DataContext = viewModel;

            // Subscribe to events
            viewModel.DialogClosed += (s, e) => Close();
        }

        // منع إدخال غير الأرقام في الحقول الرقمية
        private void NumericTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // السماح بالأرقام فقط
            var regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }

        // التحكم في اللصق للحقول الرقمية
        private void NumericTextBox_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            // التعامل مع Ctrl+V (اللصق)
            if (e.Key == Key.V && Keyboard.Modifiers == ModifierKeys.Control)
            {
                e.Handled = true;
                HandleNumericPaste(sender as TextBox);
            }
        }

        // معالجة لصق النصوص في الحقول الرقمية
        private static void HandleNumericPaste(TextBox? textBox)
        {
            if (textBox == null) return;

            try
            {
                // الحصول على النص من الحافظة
                var clipboardText = Clipboard.GetText();

                if (string.IsNullOrEmpty(clipboardText))
                    return;

                // إزالة جميع الأحرف غير الرقمية
                var numericOnly = Regex.Replace(clipboardText, @"[^\d]", "");

                if (!string.IsNullOrEmpty(numericOnly))
                {
                    // لصق الأرقام فقط
                    var selectionStart = textBox.SelectionStart;
                    var selectionLength = textBox.SelectionLength;

                    var currentText = textBox.Text;
                    var newText = currentText.Remove(selectionStart, selectionLength).Insert(selectionStart, numericOnly);

                    textBox.Text = newText;
                    textBox.SelectionStart = selectionStart + numericOnly.Length;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling numeric paste: {ex.Message}");
            }
        }
    }
}
