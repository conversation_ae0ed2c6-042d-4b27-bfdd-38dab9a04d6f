<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>

    <AssemblyTitle>Shabaka Pro</AssemblyTitle>
    <AssemblyDescription>نظام إدارة الشبكات والأجهزة المتقدم</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ApplicationIcon>Resources\shabaka-pro.ico</ApplicationIcon>
    <AssemblyCompany>Shabaka Pro</AssemblyCompany>
    <AssemblyProduct>Shabaka Pro - نظام إدارة الشبكات</AssemblyProduct>
    <AssemblyCopyright>© 2024 Shabaka Pro. جميع الحقوق محفوظة.</AssemblyCopyright>

  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="6.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.7">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="MySqlConnector" Version="2.1.13" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="6.0.2" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="EPPlus" Version="7.0.0" />
    <PackageReference Include="QuestPDF" Version="2023.12.6" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.39" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
    <None Remove="Resources\app-icon.ico" />
  </ItemGroup>

</Project>
